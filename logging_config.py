"""
Logging configuration for FastMCP Demo Server

Provides structured logging with different levels and formatters
for development and production environments.
"""

import logging
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional


class ColoredFormatter(logging.Formatter):
    """Custom formatter with colors for console output"""
    
    # ANSI color codes
    COLORS = {
        'DEBUG': '\033[36m',      # Cyan
        'INFO': '\033[32m',       # Green
        'WARNING': '\033[33m',    # Yellow
        'ERROR': '\033[31m',      # Red
        'CRITICAL': '\033[35m',   # Magenta
        'RESET': '\033[0m'        # Reset
    }
    
    def format(self, record):
        # Add color to the level name
        if record.levelname in self.COLORS:
            record.levelname = (
                f"{self.COLORS[record.levelname]}"
                f"{record.levelname}"
                f"{self.COLORS['RESET']}"
            )
        
        return super().format(record)


def setup_logging(
    level: str = "INFO",
    log_file: Optional[str] = None,
    enable_colors: bool = True
) -> logging.Logger:
    """
    Set up logging configuration for the FastMCP server.
    
    Args:
        level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Optional file path for log output
        enable_colors: Whether to use colored output for console
    
    Returns:
        Configured logger instance
    """
    
    # Create logger
    logger = logging.getLogger("fastmcp_demo")
    logger.setLevel(getattr(logging, level.upper()))
    
    # Clear any existing handlers
    logger.handlers.clear()
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(getattr(logging, level.upper()))
    
    if enable_colors and sys.stdout.isatty():
        console_format = ColoredFormatter(
            '%(asctime)s | %(levelname)s | %(name)s | %(message)s',
            datefmt='%H:%M:%S'
        )
    else:
        console_format = logging.Formatter(
            '%(asctime)s | %(levelname)s | %(name)s | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
    
    console_handler.setFormatter(console_format)
    logger.addHandler(console_handler)
    
    # File handler (if specified)
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.FileHandler(log_path)
        file_handler.setLevel(logging.DEBUG)  # Always log everything to file
        
        file_format = logging.Formatter(
            '%(asctime)s | %(levelname)s | %(name)s | %(funcName)s:%(lineno)d | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(file_format)
        logger.addHandler(file_handler)
    
    return logger


def log_server_startup(logger: logging.Logger, server_name: str, transport: str = "stdio"):
    """Log server startup information"""
    logger.info("=" * 60)
    logger.info(f"🚀 Starting {server_name}")
    logger.info(f"📅 Startup time: {datetime.now().isoformat()}")
    logger.info(f"🔗 Transport: {transport}")
    logger.info(f"🐍 Python version: {sys.version}")
    logger.info("=" * 60)


def log_tool_execution(logger: logging.Logger, tool_name: str, args: dict, execution_time: float):
    """Log tool execution details"""
    logger.info(f"🔧 Tool '{tool_name}' executed in {execution_time:.3f}s with args: {args}")


def log_error_with_context(logger: logging.Logger, error: Exception, context: dict):
    """Log error with additional context information"""
    logger.error(f"❌ Error: {type(error).__name__}: {str(error)}")
    if context:
        logger.error(f"📋 Context: {context}")


def log_resource_access(logger: logging.Logger, resource_uri: str, success: bool):
    """Log resource access attempts"""
    status = "✅ Success" if success else "❌ Failed"
    logger.info(f"📄 Resource access: {resource_uri} - {status}")


# Create a default logger instance
default_logger = setup_logging()
