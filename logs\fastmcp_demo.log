2025-07-17 13:35:44 | [32mINFO[0m | fastmcp_demo | log_server_startup:102 | ============================================================
2025-07-17 13:35:44 | [32mINFO[0m | fastmcp_demo | log_server_startup:107 | ============================================================
2025-07-17 13:39:07 | INFO | fastmcp_demo | log_server_startup:102 | ============================================================
2025-07-17 13:39:07 | INFO | fastmcp_demo | log_server_startup:103 | Starting FastMCP Demo Server
2025-07-17 13:39:07 | INFO | fastmcp_demo | log_server_startup:104 | Startup time: 2025-07-17T13:39:07.684246
2025-07-17 13:39:07 | INFO | fastmcp_demo | log_server_startup:105 | Transport: stdio
2025-07-17 13:39:07 | INFO | fastmcp_demo | log_server_startup:106 | Python version: 3.11.7 (tags/v3.11.7:fa7a6f2, Dec  4 2023, 19:24:49) [MSC v.1937 64 bit (AMD64)]
2025-07-17 13:39:07 | INFO | fastmcp_demo | log_server_startup:107 | ============================================================
2025-07-17 13:39:07 | INFO | fastmcp_demo | <module>:424 | Server provides tools for math, text processing, file operations, and more!
2025-07-17 13:39:07 | INFO | fastmcp_demo | <module>:425 | Use STDIO transport by default, or specify --transport http for web access
2025-07-17 13:39:07 | INFO | fastmcp_demo | <module>:426 | Logs are saved to logs/fastmcp_demo.log
2025-07-17 13:39:07 | INFO | fastmcp_demo | advanced_math:149 | Performing sqrt(144.0)
2025-07-17 13:39:07 | INFO | fastmcp_demo | word_count:191 | Analyzed text: 9 words, 43 characters
2025-07-17 13:39:07 | INFO | fastmcp_demo | generate_data:205 | Generating 5 items of type 'numbers'
2025-07-17 13:39:07 | INFO | fastmcp_demo | generate_data:205 | Generating 3 items of type 'names'
