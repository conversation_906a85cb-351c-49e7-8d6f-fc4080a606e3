# FastMCP Demo Server Environment Configuration
# Copy this file to .env and modify as needed

# Logging Configuration
FASTMCP_LOG_LEVEL=INFO
FASTMCP_MASK_ERROR_DETAILS=False

# Resource Configuration
FASTMCP_RESOURCE_PREFIX_FORMAT=protocol

# Server Configuration
SERVER_NAME=FastMCP Demo Server
SERVER_VERSION=1.0.0

# Security Settings
MAX_FILE_SIZE=10485760
MAX_REQUEST_TIMEOUT=30
MAX_GENERATED_ITEMS=100

# Development Settings
DEBUG_MODE=False
AUTO_RELOAD=False
INSPECTOR_ENABLED=False

# Transport Settings
DEFAULT_TRANSPORT=stdio
HTTP_HOST=127.0.0.1
HTTP_PORT=8000
HTTP_PATH=/mcp/

# Feature Flags
ENABLE_MATH_OPERATIONS=True
ENABLE_TEXT_PROCESSING=True
ENABLE_FILE_OPERATIONS=True
ENABLE_NETWORK_OPERATIONS=True
ENABLE_DATA_GENERATION=True

# Monitoring
METRICS_ENABLED=False
PERFORMANCE_LOGGING=False
ERROR_REPORTING_ENABLED=True
